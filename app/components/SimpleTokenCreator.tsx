'use client';

import { useState } from 'react';
import { useCurrentAccount } from '@iota/dapp-kit';
import { generateMoveSource } from '../lib/tokenBytecode';

interface TokenConfig {
  name: string;
  symbol: string;
  description: string;
  decimals: number;
  iconUrl?: string;
}

export function SimpleTokenCreator() {
  const currentAccount = useCurrentAccount();
  
  const [tokenConfig, setTokenConfig] = useState<TokenConfig>({
    name: '',
    symbol: '',
    description: '',
    decimals: 6,
    iconUrl: '',
  });
  
  const [showCode, setShowCode] = useState(false);
  const [generatedCode, setGeneratedCode] = useState<{
    moveCode: string;
    moduleName: string;
    structName: string;
    witnessName: string;
  } | null>(null);

  const handleInputChange = (field: keyof TokenConfig, value: string | number) => {
    setTokenConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateToken = () => {
    if (!tokenConfig.name || !tokenConfig.symbol || !tokenConfig.description) {
      alert('Please fill in all required fields');
      return;
    }

    const code = generateMoveSource(tokenConfig);
    setGeneratedCode(code);
    setShowCode(true);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  const generateMoveToml = () => {
    if (!generatedCode) return '';
    
    return `[package]
name = "${generatedCode.moduleName}"
edition = "2024.beta"

[dependencies]
Iota = { git = "https://github.com/iotaledger/iota.git", subdir = "crates/iota-framework/packages/iota-framework", rev = "framework/testnet" }

[addresses]
${generatedCode.moduleName} = "0x0"`;
  };

  const generateDeployScript = () => {
    if (!generatedCode) return '';
    
    return `#!/bin/bash

# IOTA Token Deployment Script
# Generated for: ${tokenConfig.name} (${tokenConfig.symbol})

echo "🚀 Deploying ${tokenConfig.name} token..."

# Create project directory
mkdir -p ${generatedCode.moduleName}
cd ${generatedCode.moduleName}
mkdir -p sources

# Create Move.toml
cat > Move.toml << 'EOF'
${generateMoveToml()}
EOF

# Create Move source file
cat > sources/${generatedCode.moduleName}.move << 'EOF'
${generatedCode.moveCode}
EOF

echo "📦 Files created successfully!"
echo "🔨 Building Move package..."

# Build the package
if iota move build; then
    echo "✅ Package built successfully!"
    
    # Check if user has IOTA CLI configured
    if iota client active-address &> /dev/null; then
        echo "📍 Active address: $(iota client active-address)"
        echo "💰 Checking balance..."
        iota client balance
        
        echo ""
        echo "📤 Ready to deploy! Run the following command:"
        echo "   iota client publish --gas-budget 20000000"
        echo ""
        read -p "Deploy now? (y/N): " -n 1 -r
        echo
        
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo "🚀 Deploying to testnet..."
            iota client publish --gas-budget 20000000
        fi
    else
        echo "⚙️ IOTA CLI not configured. Please run:"
        echo "   iota client"
        echo "   # Select testnet and ed25519"
        echo "   iota client faucet"
        echo "   iota client publish --gas-budget 20000000"
    fi
else
    echo "❌ Build failed. Please check the Move code."
fi`;
  };

  if (showCode && generatedCode) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-green-600 mb-2">🎉 Token Code Generated!</h2>
          <p className="text-gray-600">Your {tokenConfig.name} ({tokenConfig.symbol}) token is ready to deploy</p>
        </div>

        {/* Token Info */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-700 mb-2">Token Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><span className="font-medium">Name:</span> {tokenConfig.name}</div>
            <div><span className="font-medium">Symbol:</span> {tokenConfig.symbol}</div>
            <div><span className="font-medium">Decimals:</span> {tokenConfig.decimals}</div>
            <div><span className="font-medium">Module:</span> {generatedCode.moduleName}</div>
            <div><span className="font-medium">Coin Type:</span> {generatedCode.witnessName}</div>
          </div>
        </div>

        {/* One-Click Deploy Script */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">🚀 One-Click Deploy Script</h3>
            <button
              onClick={() => copyToClipboard(generateDeployScript())}
              className="text-sm bg-green-100 hover:bg-green-200 px-3 py-1 rounded transition-colors"
            >
              Copy Deploy Script
            </button>
          </div>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm overflow-x-auto max-h-48">
            <pre>{generateDeployScript()}</pre>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            💡 Save this as <code>deploy.sh</code>, make it executable with <code>chmod +x deploy.sh</code>, then run <code>./deploy.sh</code>
          </p>
        </div>

        {/* Move.toml */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">📄 Move.toml</h3>
            <button
              onClick={() => copyToClipboard(generateMoveToml())}
              className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded transition-colors"
            >
              Copy
            </button>
          </div>
          <pre className="bg-gray-900 text-white p-4 rounded-lg text-sm overflow-x-auto">
            {generateMoveToml()}
          </pre>
        </div>

        {/* Move Code */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">📝 sources/{generatedCode.moduleName}.move</h3>
            <button
              onClick={() => copyToClipboard(generatedCode.moveCode)}
              className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded transition-colors"
            >
              Copy
            </button>
          </div>
          <pre className="bg-gray-900 text-white p-4 rounded-lg text-sm overflow-x-auto max-h-96">
            {generatedCode.moveCode}
          </pre>
        </div>

        {/* Quick Commands */}
        <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-700 mb-2">⚡ Quick Commands</h3>
          <div className="space-y-2 text-sm font-mono">
            <div className="flex items-center justify-between bg-yellow-100 p-2 rounded">
              <span>mkdir {generatedCode.moduleName} && cd {generatedCode.moduleName}</span>
              <button 
                onClick={() => copyToClipboard(`mkdir ${generatedCode.moduleName} && cd ${generatedCode.moduleName}`)}
                className="text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded"
              >
                Copy
              </button>
            </div>
            <div className="flex items-center justify-between bg-yellow-100 p-2 rounded">
              <span>iota move build</span>
              <button 
                onClick={() => copyToClipboard('iota move build')}
                className="text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded"
              >
                Copy
              </button>
            </div>
            <div className="flex items-center justify-between bg-yellow-100 p-2 rounded">
              <span>iota client publish --gas-budget 20000000</span>
              <button 
                onClick={() => copyToClipboard('iota client publish --gas-budget 20000000')}
                className="text-xs bg-yellow-200 hover:bg-yellow-300 px-2 py-1 rounded"
              >
                Copy
              </button>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <button
            onClick={() => setShowCode(false)}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
          >
            ← Back to Form
          </button>
          <button
            onClick={() => {
              setShowCode(false);
              setGeneratedCode(null);
              setTokenConfig({
                name: '',
                symbol: '',
                description: '',
                decimals: 6,
                iconUrl: '',
              });
            }}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Another Token
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Create Your IOTA Token</h2>
        <p className="text-gray-600">Generate Move smart contract and deployment scripts</p>
      </div>

      <form onSubmit={(e) => { e.preventDefault(); generateToken(); }} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Token Name *
          </label>
          <input
            type="text"
            id="name"
            value={tokenConfig.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="e.g., My Awesome Token"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
            Token Symbol *
          </label>
          <input
            type="text"
            id="symbol"
            value={tokenConfig.symbol}
            onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
            placeholder="e.g., MAT"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxLength={10}
            required
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            value={tokenConfig.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe your token..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
            Decimals
          </label>
          <select
            id="decimals"
            value={tokenConfig.decimals}
            onChange={(e) => handleInputChange('decimals', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>0 (No decimals)</option>
            <option value={2}>2 (Like cents)</option>
            <option value={6}>6 (Recommended)</option>
            <option value={8}>8 (Like Bitcoin)</option>
            <option value={18}>18 (Like Ethereum)</option>
          </select>
        </div>

        <div>
          <label htmlFor="iconUrl" className="block text-sm font-medium text-gray-700 mb-2">
            Icon URL (Optional)
          </label>
          <input
            type="url"
            id="iconUrl"
            value={tokenConfig.iconUrl}
            onChange={(e) => handleInputChange('iconUrl', e.target.value)}
            placeholder="https://example.com/icon.png"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          Generate Token Code
        </button>
      </form>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-700 mb-2">🎯 What you'll get</h3>
        <div className="text-sm text-blue-800 space-y-1">
          <div>✅ Complete Move smart contract source code</div>
          <div>✅ Move.toml configuration file</div>
          <div>✅ One-click deployment script</div>
          <div>✅ Step-by-step instructions</div>
          <div>✅ Copy-to-clipboard for all code</div>
        </div>
      </div>
    </div>
  );
}
