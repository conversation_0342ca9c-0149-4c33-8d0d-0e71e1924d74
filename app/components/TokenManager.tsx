'use client';

import { useState } from 'react';
import { useCurrentAccount, useSignAndExecuteTransaction, useIotaClient } from '@iota/dapp-kit';
import { Transaction } from '@iota/iota-sdk/transactions';

interface TokenInfo {
  packageId: string;
  treasuryCap: string;
  coinType: string;
  name: string;
  symbol: string;
}

export function TokenManager() {
  const currentAccount = useCurrentAccount();
  const client = useIotaClient();
  const { mutate: signAndExecute } = useSignAndExecuteTransaction();
  
  const [tokenInfo, setTokenInfo] = useState<TokenInfo>({
    packageId: '',
    treasuryCap: '',
    coinType: '',
    name: '',
    symbol: '',
  });
  
  const [mintAmount, setMintAmount] = useState('');
  const [transferAmount, setTransferAmount] = useState('');
  const [transferRecipient, setTransferRecipient] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleMint = async () => {
    if (!currentAccount || !tokenInfo.packageId || !tokenInfo.treasuryCap || !mintAmount) {
      setMessage({ type: 'error', text: 'Please fill in all required fields' });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const tx = new Transaction();
      
      // Call the mint function
      const moduleName = tokenInfo.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
      
      tx.moveCall({
        target: `${tokenInfo.packageId}::${moduleName}::mint`,
        arguments: [
          tx.object(tokenInfo.treasuryCap),
          tx.pure.u64(mintAmount),
          tx.pure.address(currentAccount.address),
        ],
      });

      signAndExecute(
        { transaction: tx },
        {
          onSuccess: (result) => {
            setMessage({ 
              type: 'success', 
              text: `Successfully minted ${mintAmount} tokens! Transaction: ${result.digest}` 
            });
            setMintAmount('');
            setIsLoading(false);
          },
          onError: (error) => {
            setMessage({ type: 'error', text: `Minting failed: ${error.message}` });
            setIsLoading(false);
          },
        }
      );
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
      setIsLoading(false);
    }
  };

  const handleTransfer = async () => {
    if (!currentAccount || !tokenInfo.coinType || !transferAmount || !transferRecipient) {
      setMessage({ type: 'error', text: 'Please fill in all required fields' });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      // Get user's coins of this type
      const coins = await client.getCoins({
        owner: currentAccount.address,
        coinType: tokenInfo.coinType,
      });

      if (coins.data.length === 0) {
        setMessage({ type: 'error', text: 'No coins of this type found in your wallet' });
        setIsLoading(false);
        return;
      }

      const tx = new Transaction();
      
      // Merge coins if needed and split the required amount
      const [coin] = tx.splitCoins(tx.object(coins.data[0].coinObjectId), [
        tx.pure.u64(transferAmount),
      ]);

      // Transfer the coin
      tx.transferObjects([coin], transferRecipient);

      signAndExecute(
        { transaction: tx },
        {
          onSuccess: (result) => {
            setMessage({ 
              type: 'success', 
              text: `Successfully transferred ${transferAmount} tokens! Transaction: ${result.digest}` 
            });
            setTransferAmount('');
            setTransferRecipient('');
            setIsLoading(false);
          },
          onError: (error) => {
            setMessage({ type: 'error', text: `Transfer failed: ${error.message}` });
            setIsLoading(false);
          },
        }
      );
    } catch (error) {
      setMessage({ 
        type: 'error', 
        text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
      setIsLoading(false);
    }
  };

  if (!currentAccount) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Token Management</h2>
          <p className="text-gray-600">Please connect your wallet to manage tokens</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Manage Your Tokens</h2>
        <p className="text-gray-600">Mint and transfer your created tokens</p>
      </div>

      {message && (
        <div className={`mb-6 p-4 rounded-lg ${
          message.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-700' 
            : 'bg-red-50 border border-red-200 text-red-700'
        }`}>
          <p>{message.text}</p>
        </div>
      )}

      {/* Token Information Form */}
      <div className="mb-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-700 mb-4">Token Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Token Name
            </label>
            <input
              type="text"
              value={tokenInfo.name}
              onChange={(e) => setTokenInfo(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., My Token"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Token Symbol
            </label>
            <input
              type="text"
              value={tokenInfo.symbol}
              onChange={(e) => setTokenInfo(prev => ({ ...prev, symbol: e.target.value }))}
              placeholder="e.g., MTK"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Package ID
            </label>
            <input
              type="text"
              value={tokenInfo.packageId}
              onChange={(e) => setTokenInfo(prev => ({ ...prev, packageId: e.target.value }))}
              placeholder="0x..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Treasury Cap ID
            </label>
            <input
              type="text"
              value={tokenInfo.treasuryCap}
              onChange={(e) => setTokenInfo(prev => ({ ...prev, treasuryCap: e.target.value }))}
              placeholder="0x..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Coin Type
            </label>
            <input
              type="text"
              value={tokenInfo.coinType}
              onChange={(e) => setTokenInfo(prev => ({ ...prev, coinType: e.target.value }))}
              placeholder="0x...::module::COIN"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Mint Tokens */}
      <div className="mb-8 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold text-blue-700 mb-4">Mint Tokens</h3>
        <div className="flex gap-4">
          <input
            type="number"
            value={mintAmount}
            onChange={(e) => setMintAmount(e.target.value)}
            placeholder="Amount to mint"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={handleMint}
            disabled={isLoading || !tokenInfo.packageId || !tokenInfo.treasuryCap}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Minting...' : 'Mint'}
          </button>
        </div>
      </div>

      {/* Transfer Tokens */}
      <div className="p-4 bg-green-50 rounded-lg">
        <h3 className="font-semibold text-green-700 mb-4">Transfer Tokens</h3>
        <div className="space-y-4">
          <input
            type="number"
            value={transferAmount}
            onChange={(e) => setTransferAmount(e.target.value)}
            placeholder="Amount to transfer"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
          />
          <input
            type="text"
            value={transferRecipient}
            onChange={(e) => setTransferRecipient(e.target.value)}
            placeholder="Recipient address (0x...)"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
          />
          <button
            onClick={handleTransfer}
            disabled={isLoading || !tokenInfo.coinType}
            className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? 'Transferring...' : 'Transfer'}
          </button>
        </div>
      </div>
    </div>
  );
}
