'use client';

import { useState } from 'react';
import { useCurrentAccount, useSignAndExecuteTransaction } from '@iota/dapp-kit';
import { Transaction } from '@iota/iota-sdk/transactions';

interface TokenConfig {
  name: string;
  symbol: string;
  description: string;
  decimals: number;
  iconUrl?: string;
}

interface TokenCreationResult {
  packageId: string;
  treasuryCap: string;
  coinType: string;
  transactionDigest: string;
}

export function TokenCreationForm() {
  const currentAccount = useCurrentAccount();
  const { mutate: signAndExecute } = useSignAndExecuteTransaction();
  
  const [tokenConfig, setTokenConfig] = useState<TokenConfig>({
    name: '',
    symbol: '',
    description: '',
    decimals: 6,
    iconUrl: '',
  });
  
  const [isCreating, setIsCreating] = useState(false);
  const [result, setResult] = useState<TokenCreationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof TokenConfig, value: string | number) => {
    setTokenConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateMoveCode = (config: TokenConfig) => {
    const moduleName = config.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const structName = config.symbol.toUpperCase();
    
    return `module ${moduleName}::${moduleName} {
    use iota::coin::{Self, Coin, TreasuryCap};
    use iota::url::{Self, Url};
    use std::option;

    /// The type identifier of coin. The coin will have a type
    /// tag of kind: \`Coin<package_object::${moduleName}::${structName}>\`
    public struct ${structName} has drop {}

    #[allow(lint(share_owned))]
    fun init(witness: ${structName}, ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<${structName}>(
            witness,
            ${config.decimals},
            b"${config.symbol}",
            b"${config.name}",
            b"${config.description}",
            ${config.iconUrl ? `option::some<Url>(url::new_unsafe_from_bytes(b"${config.iconUrl}"))` : 'option::none<Url>()'},
            ctx
        );
        transfer::public_freeze_object(metadata);
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx))
    }

    public entry fun mint(
        treasury_cap: &mut TreasuryCap<${structName}>, 
        amount: u64, 
        recipient: address, 
        ctx: &mut TxContext
    ) {
        coin::mint_and_transfer(treasury_cap, amount, recipient, ctx)
    }

    public entry fun burn(treasury_cap: &mut TreasuryCap<${structName}>, coin: Coin<${structName}>) {
        coin::burn(treasury_cap, coin);
    }
}`;
  };

  const generateMoveToml = (config: TokenConfig) => {
    const moduleName = config.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    
    return `[package]
name = "${moduleName}"
edition = "2024.beta"

[dependencies]
Iota = { git = "https://github.com/iotaledger/iota.git", subdir = "crates/iota-framework/packages/iota-framework", rev = "framework/testnet" }

[addresses]
${moduleName} = "0x0"`;
  };

  const createToken = async () => {
    if (!currentAccount) {
      setError('Please connect your wallet first');
      return;
    }

    if (!tokenConfig.name || !tokenConfig.symbol || !tokenConfig.description) {
      setError('Please fill in all required fields');
      return;
    }

    setIsCreating(true);
    setError(null);

    try {
      // Call our API to generate the Move package
      const response = await fetch('/api/create-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(tokenConfig),
      });

      const data = await response.json();

      if (data.success) {
        // For now, show the generated code and instructions
        // In a full implementation, this would deploy directly
        setResult({
          packageId: 'Generated - Deploy manually',
          treasuryCap: 'Will be created after deployment',
          coinType: `<package-id>::${data.packageName}::${data.structName}`,
          transactionDigest: 'Manual deployment required',
        });
      } else {
        setError(data.error || 'Failed to generate token package');
      }

      setIsCreating(false);

    } catch (error) {
      console.error('Error creating token:', error);
      setError(`Error creating token: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsCreating(false);
    }
  };

  if (result) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-green-600 mb-2">🎉 Token Created Successfully!</h2>
          <p className="text-gray-600">Your token has been deployed to the IOTA testnet</p>
        </div>
        
        <div className="space-y-4 mb-6">
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-700 mb-2">Token Information</h3>
            <div className="space-y-2 text-sm">
              <div><span className="font-medium">Name:</span> {tokenConfig.name}</div>
              <div><span className="font-medium">Symbol:</span> {tokenConfig.symbol}</div>
              <div><span className="font-medium">Decimals:</span> {tokenConfig.decimals}</div>
            </div>
          </div>
          
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-700 mb-2">Blockchain Details</h3>
            <div className="space-y-2 text-sm break-all">
              <div><span className="font-medium">Package ID:</span> {result.packageId}</div>
              <div><span className="font-medium">Treasury Cap:</span> {result.treasuryCap}</div>
              <div><span className="font-medium">Coin Type:</span> {result.coinType}</div>
              <div><span className="font-medium">Transaction:</span> {result.transactionDigest}</div>
            </div>
          </div>
        </div>
        
        <div className="flex gap-4">
          <a
            href={`https://explorer.iota.org/txblock/${result.transactionDigest}?network=testnet`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-center hover:bg-blue-700 transition-colors"
          >
            View on Explorer
          </a>
          <button
            onClick={() => {
              setResult(null);
              setTokenConfig({
                name: '',
                symbol: '',
                description: '',
                decimals: 6,
                iconUrl: '',
              });
            }}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Create Another Token
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Create Your IOTA Token</h2>
        <p className="text-gray-600">Deploy your own token on the IOTA blockchain</p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      <form onSubmit={(e) => { e.preventDefault(); createToken(); }} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Token Name *
          </label>
          <input
            type="text"
            id="name"
            value={tokenConfig.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="e.g., My Awesome Token"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
            Token Symbol *
          </label>
          <input
            type="text"
            id="symbol"
            value={tokenConfig.symbol}
            onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
            placeholder="e.g., MAT"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxLength={10}
            required
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            value={tokenConfig.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe your token..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
            Decimals
          </label>
          <select
            id="decimals"
            value={tokenConfig.decimals}
            onChange={(e) => handleInputChange('decimals', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>0 (No decimals)</option>
            <option value={2}>2 (Like cents)</option>
            <option value={6}>6 (Recommended)</option>
            <option value={8}>8 (Like Bitcoin)</option>
            <option value={18}>18 (Like Ethereum)</option>
          </select>
        </div>

        <div>
          <label htmlFor="iconUrl" className="block text-sm font-medium text-gray-700 mb-2">
            Icon URL (Optional)
          </label>
          <input
            type="url"
            id="iconUrl"
            value={tokenConfig.iconUrl}
            onChange={(e) => handleInputChange('iconUrl', e.target.value)}
            placeholder="https://example.com/icon.png"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <button
          type="submit"
          disabled={isCreating || !currentAccount}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isCreating ? 'Creating Token...' : 'Create Token'}
        </button>
      </form>

      {!currentAccount && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-700 text-center">
            Please connect your wallet to create a token
          </p>
        </div>
      )}
    </div>
  );
}
