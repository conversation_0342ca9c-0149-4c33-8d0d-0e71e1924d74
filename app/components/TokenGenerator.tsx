'use client';

import { useState } from 'react';
import { useCurrentAccount } from '@iota/dapp-kit';

interface TokenConfig {
  name: string;
  symbol: string;
  description: string;
  decimals: number;
  iconUrl?: string;
}

export function TokenGenerator() {
  const currentAccount = useCurrentAccount();
  
  const [tokenConfig, setTokenConfig] = useState<TokenConfig>({
    name: '',
    symbol: '',
    description: '',
    decimals: 6,
    iconUrl: '',
  });
  
  const [showInstructions, setShowInstructions] = useState(false);

  const handleInputChange = (field: keyof TokenConfig, value: string | number) => {
    setTokenConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateMoveCode = (config: TokenConfig) => {
    const moduleName = config.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const structName = config.symbol.toUpperCase();
    
    return `module ${moduleName}::${moduleName} {
    use iota::coin::{Self, Coin, TreasuryCap};
    use iota::url::{Self, Url};
    use std::option;

    /// The type identifier of coin. The coin will have a type
    /// tag of kind: \`Coin<package_object::${moduleName}::${structName}>\`
    public struct ${structName} has drop {}

    #[allow(lint(share_owned))]
    fun init(witness: ${structName}, ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency<${structName}>(
            witness,
            ${config.decimals},
            b"${config.symbol}",
            b"${config.name}",
            b"${config.description}",
            ${config.iconUrl ? `option::some<Url>(url::new_unsafe_from_bytes(b"${config.iconUrl}"))` : 'option::none<Url>()'},
            ctx
        );
        transfer::public_freeze_object(metadata);
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx))
    }

    public entry fun mint(
        treasury_cap: &mut TreasuryCap<${structName}>, 
        amount: u64, 
        recipient: address, 
        ctx: &mut TxContext
    ) {
        coin::mint_and_transfer(treasury_cap, amount, recipient, ctx)
    }

    public entry fun burn(treasury_cap: &mut TreasuryCap<${structName}>, coin: Coin<${structName}>) {
        coin::burn(treasury_cap, coin);
    }
}`;
  };

  const generateMoveToml = (config: TokenConfig) => {
    const moduleName = config.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    
    return `[package]
name = "${moduleName}"
edition = "2024.beta"

[dependencies]
Iota = { git = "https://github.com/iotaledger/iota.git", subdir = "crates/iota-framework/packages/iota-framework", rev = "framework/testnet" }

[addresses]
${moduleName} = "0x0"`;
  };

  const generateInstructions = () => {
    const moduleName = tokenConfig.name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    const structName = tokenConfig.symbol.toUpperCase();
    
    return {
      moduleName,
      structName,
      moveCode: generateMoveCode(tokenConfig),
      moveToml: generateMoveToml(tokenConfig),
      commands: [
        `mkdir ${moduleName}`,
        `cd ${moduleName}`,
        `mkdir sources`,
        `# Create Move.toml file with the content shown below`,
        `# Create sources/${moduleName}.move file with the Move code shown below`,
        `iota move build`,
        `iota client faucet`,
        `iota client publish --gas-budget 20000000`,
      ]
    };
  };

  const handleGenerate = () => {
    if (!tokenConfig.name || !tokenConfig.symbol || !tokenConfig.description) {
      alert('Please fill in all required fields');
      return;
    }
    setShowInstructions(true);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    alert('Copied to clipboard!');
  };

  if (showInstructions) {
    const instructions = generateInstructions();
    
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-green-600 mb-2">🎉 Token Package Generated!</h2>
          <p className="text-gray-600">Follow these steps to deploy your token</p>
        </div>

        {/* Token Info */}
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-700 mb-2">Token Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><span className="font-medium">Name:</span> {tokenConfig.name}</div>
            <div><span className="font-medium">Symbol:</span> {tokenConfig.symbol}</div>
            <div><span className="font-medium">Decimals:</span> {tokenConfig.decimals}</div>
            <div><span className="font-medium">Module:</span> {instructions.moduleName}</div>
          </div>
        </div>

        {/* Commands */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">1. Setup Commands</h3>
            <button
              onClick={() => copyToClipboard(instructions.commands.join('\n'))}
              className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded transition-colors"
            >
              Copy Commands
            </button>
          </div>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
            {instructions.commands.map((cmd, i) => (
              <div key={i} className={cmd.startsWith('#') ? 'text-gray-500' : ''}>
                {cmd.startsWith('#') ? cmd : `$ ${cmd}`}
              </div>
            ))}
          </div>
        </div>

        {/* Move.toml */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">2. Move.toml</h3>
            <button
              onClick={() => copyToClipboard(instructions.moveToml)}
              className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded transition-colors"
            >
              Copy
            </button>
          </div>
          <pre className="bg-gray-900 text-white p-4 rounded-lg text-sm overflow-x-auto">
            {instructions.moveToml}
          </pre>
        </div>

        {/* Move Code */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-gray-700">3. sources/{instructions.moduleName}.move</h3>
            <button
              onClick={() => copyToClipboard(instructions.moveCode)}
              className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded transition-colors"
            >
              Copy
            </button>
          </div>
          <pre className="bg-gray-900 text-white p-4 rounded-lg text-sm overflow-x-auto max-h-96">
            {instructions.moveCode}
          </pre>
        </div>

        {/* Next Steps */}
        <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-700 mb-2">📋 Next Steps After Deployment</h3>
          <div className="text-sm text-yellow-800 space-y-1">
            <div>1. Save the Package ID and Treasury Cap from the deployment output</div>
            <div>2. Use the "Manage Tokens" tab to mint and transfer tokens</div>
            <div>3. Your coin type will be: <code className="bg-yellow-200 px-1 rounded">{'<package-id>'}::{instructions.moduleName}::{instructions.structName}</code></div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <button
            onClick={() => setShowInstructions(false)}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
          >
            ← Back to Form
          </button>
          <button
            onClick={() => {
              setShowInstructions(false);
              setTokenConfig({
                name: '',
                symbol: '',
                description: '',
                decimals: 6,
                iconUrl: '',
              });
            }}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Another Token
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Generate IOTA Token</h2>
        <p className="text-gray-600">Create Move code and deployment instructions for your token</p>
      </div>

      <form onSubmit={(e) => { e.preventDefault(); handleGenerate(); }} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Token Name *
          </label>
          <input
            type="text"
            id="name"
            value={tokenConfig.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="e.g., My Awesome Token"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
            Token Symbol *
          </label>
          <input
            type="text"
            id="symbol"
            value={tokenConfig.symbol}
            onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
            placeholder="e.g., MAT"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxLength={10}
            required
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            value={tokenConfig.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe your token..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>

        <div>
          <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
            Decimals
          </label>
          <select
            id="decimals"
            value={tokenConfig.decimals}
            onChange={(e) => handleInputChange('decimals', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value={0}>0 (No decimals)</option>
            <option value={2}>2 (Like cents)</option>
            <option value={6}>6 (Recommended)</option>
            <option value={8}>8 (Like Bitcoin)</option>
            <option value={18}>18 (Like Ethereum)</option>
          </select>
        </div>

        <div>
          <label htmlFor="iconUrl" className="block text-sm font-medium text-gray-700 mb-2">
            Icon URL (Optional)
          </label>
          <input
            type="url"
            id="iconUrl"
            value={tokenConfig.iconUrl}
            onChange={(e) => handleInputChange('iconUrl', e.target.value)}
            placeholder="https://example.com/icon.png"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <button
          type="submit"
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          Generate Token Package
        </button>
      </form>

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-700 mb-2">💡 How it works</h3>
        <div className="text-sm text-blue-800 space-y-1">
          <div>1. Fill in your token details above</div>
          <div>2. Generate Move smart contract code</div>
          <div>3. Follow the CLI instructions to deploy</div>
          <div>4. Use the "Manage Tokens" tab to interact with your token</div>
        </div>
      </div>
    </div>
  );
}
