'use client';

import { useState } from 'react';
import { useCurrentAccount, useSignAndExecuteTransaction, useIotaClient } from '@iota/dapp-kit';
import { Transaction } from '@iota/iota-sdk/transactions';

interface TokenConfig {
  name: string;
  symbol: string;
  description: string;
  decimals: number;
  iconUrl?: string;
}

interface TokenCreationResult {
  packageId: string;
  treasuryCap: string;
  coinType: string;
  transactionDigest: string;
  explorerUrl: string;
}

export function EndToEndTokenCreator() {
  const currentAccount = useCurrentAccount();
  const client = useIotaClient();
  const { mutate: signAndExecute } = useSignAndExecuteTransaction();
  
  const [tokenConfig, setTokenConfig] = useState<TokenConfig>({
    name: '',
    symbol: '',
    description: '',
    decimals: 6,
    iconUrl: '',
  });
  
  const [isCreating, setIsCreating] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [result, setResult] = useState<TokenCreationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (field: keyof TokenConfig, value: string | number) => {
    setTokenConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const createToken = async () => {
    if (!currentAccount) {
      setError('Please connect your wallet first');
      return;
    }

    if (!tokenConfig.name || !tokenConfig.symbol || !tokenConfig.description) {
      setError('Please fill in all required fields');
      return;
    }

    setIsCreating(true);
    setError(null);
    setCurrentStep('Compiling Move package...');

    try {
      // Step 1: Compile Move package on server
      const compileResponse = await fetch('/api/create-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...tokenConfig,
          userAddress: currentAccount.address,
        }),
      });

      const compileData = await compileResponse.json();

      if (!compileData.success) {
        if (compileData.needsManualDeployment) {
          setError('Compilation successful but automatic deployment not available. Please use CLI tools.');
        } else {
          setError(compileData.error || 'Failed to compile Move package');
        }
        setIsCreating(false);
        return;
      }

      setCurrentStep('Creating deployment transaction...');

      // Step 2: Create and sign deployment transaction
      const tx = new Transaction();

      // Publish the compiled Move package
      const [upgradeCap] = tx.publish(
        compileData.compiledModules,
        compileData.dependencies
      );

      // Transfer the upgrade capability to the user
      tx.transferObjects([upgradeCap], currentAccount.address);

      setCurrentStep('Waiting for wallet signature...');

      // Step 3: Sign and execute the transaction
      signAndExecute(
        {
          transaction: tx,
        },
        {
          onSuccess: async (result) => {
            setCurrentStep('Processing transaction result...');
            
            try {
              // Extract package information from transaction result
              const packageId = result.objectChanges?.find(
                (change) => change.type === 'published'
              )?.packageId;

              if (!packageId) {
                setError('Could not extract package ID from transaction result');
                setIsCreating(false);
                return;
              }

              // Find the Treasury Cap object
              const treasuryCapChange = result.objectChanges?.find(
                (change) => 
                  change.type === 'created' && 
                  change.objectType?.includes('TreasuryCap')
              );

              const treasuryCap = treasuryCapChange?.objectId;

              if (!treasuryCap) {
                setError('Could not extract Treasury Cap from transaction result');
                setIsCreating(false);
                return;
              }

              // Construct coin type
              const coinType = `${packageId}::${compileData.packageName}::${compileData.structName}`;
              const explorerUrl = `https://explorer.iota.org/txblock/${result.digest}?network=testnet`;

              setResult({
                packageId,
                treasuryCap,
                coinType,
                transactionDigest: result.digest,
                explorerUrl,
              });

              setCurrentStep('Token created successfully!');
              setIsCreating(false);

            } catch (error) {
              console.error('Error processing transaction result:', error);
              setError('Transaction successful but failed to extract token information');
              setIsCreating(false);
            }
          },
          onError: (error) => {
            console.error('Transaction failed:', error);
            setError(`Transaction failed: ${error.message}`);
            setIsCreating(false);
            setCurrentStep('');
          },
        }
      );

    } catch (error) {
      console.error('Error creating token:', error);
      setError(`Error creating token: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setIsCreating(false);
      setCurrentStep('');
    }
  };

  if (result) {
    return (
      <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
        <div className="text-center mb-6">
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-2xl font-bold text-green-600 mb-2">Token Created Successfully!</h2>
          <p className="text-gray-600">Your token has been deployed to the IOTA testnet</p>
        </div>
        
        <div className="space-y-4 mb-6">
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-700 mb-2">Token Information</h3>
            <div className="space-y-2 text-sm">
              <div><span className="font-medium">Name:</span> {tokenConfig.name}</div>
              <div><span className="font-medium">Symbol:</span> {tokenConfig.symbol}</div>
              <div><span className="font-medium">Decimals:</span> {tokenConfig.decimals}</div>
              <div><span className="font-medium">Description:</span> {tokenConfig.description}</div>
            </div>
          </div>
          
          <div className="p-4 bg-blue-50 rounded-lg">
            <h3 className="font-semibold text-blue-700 mb-2">Blockchain Details</h3>
            <div className="space-y-2 text-sm">
              <div className="break-all">
                <span className="font-medium">Package ID:</span> 
                <code className="ml-2 bg-blue-100 px-1 rounded">{result.packageId}</code>
              </div>
              <div className="break-all">
                <span className="font-medium">Treasury Cap:</span> 
                <code className="ml-2 bg-blue-100 px-1 rounded">{result.treasuryCap}</code>
              </div>
              <div className="break-all">
                <span className="font-medium">Coin Type:</span> 
                <code className="ml-2 bg-blue-100 px-1 rounded">{result.coinType}</code>
              </div>
            </div>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <h3 className="font-semibold text-green-700 mb-2">🚀 Next Steps</h3>
            <div className="text-sm text-green-800 space-y-1">
              <div>1. Use the "Manage Tokens" tab to mint your first tokens</div>
              <div>2. Share your coin type with others for transfers</div>
              <div>3. View your transaction on the IOTA explorer</div>
            </div>
          </div>
        </div>
        
        <div className="flex gap-4">
          <a
            href={result.explorerUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg text-center hover:bg-blue-700 transition-colors"
          >
            View on Explorer
          </a>
          <button
            onClick={() => {
              setResult(null);
              setTokenConfig({
                name: '',
                symbol: '',
                description: '',
                decimals: 6,
                iconUrl: '',
              });
            }}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
          >
            Create Another Token
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Create Your IOTA Token</h2>
        <p className="text-gray-600">One-click token creation and deployment</p>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {isCreating && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            <p className="text-blue-700">{currentStep}</p>
          </div>
        </div>
      )}

      <form onSubmit={(e) => { e.preventDefault(); createToken(); }} className="space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Token Name *
          </label>
          <input
            type="text"
            id="name"
            value={tokenConfig.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="e.g., My Awesome Token"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
            disabled={isCreating}
          />
        </div>

        <div>
          <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
            Token Symbol *
          </label>
          <input
            type="text"
            id="symbol"
            value={tokenConfig.symbol}
            onChange={(e) => handleInputChange('symbol', e.target.value.toUpperCase())}
            placeholder="e.g., MAT"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxLength={10}
            required
            disabled={isCreating}
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Description *
          </label>
          <textarea
            id="description"
            value={tokenConfig.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Describe your token..."
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
            disabled={isCreating}
          />
        </div>

        <div>
          <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
            Decimals
          </label>
          <select
            id="decimals"
            value={tokenConfig.decimals}
            onChange={(e) => handleInputChange('decimals', parseInt(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isCreating}
          >
            <option value={0}>0 (No decimals)</option>
            <option value={2}>2 (Like cents)</option>
            <option value={6}>6 (Recommended)</option>
            <option value={8}>8 (Like Bitcoin)</option>
            <option value={18}>18 (Like Ethereum)</option>
          </select>
        </div>

        <div>
          <label htmlFor="iconUrl" className="block text-sm font-medium text-gray-700 mb-2">
            Icon URL (Optional)
          </label>
          <input
            type="url"
            id="iconUrl"
            value={tokenConfig.iconUrl}
            onChange={(e) => handleInputChange('iconUrl', e.target.value)}
            placeholder="https://example.com/icon.png"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isCreating}
          />
        </div>

        <button
          type="submit"
          disabled={isCreating || !currentAccount}
          className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
        >
          {isCreating ? 'Creating Token...' : 'Create Token'}
        </button>
      </form>

      {!currentAccount && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-700 text-center">
            Please connect your wallet to create a token
          </p>
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="font-semibold text-blue-700 mb-2">✨ How it works</h3>
        <div className="text-sm text-blue-800 space-y-1">
          <div>1. Fill in your token details</div>
          <div>2. Click "Create Token" - we'll compile the Move smart contract</div>
          <div>3. Sign the deployment transaction with your wallet</div>
          <div>4. Your token is deployed and ready to use!</div>
        </div>
      </div>
    </div>
  );
}
